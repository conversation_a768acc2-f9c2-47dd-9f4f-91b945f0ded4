# Kung Fu Video Detector Workflow

This N8N workflow automatically scans a folder for MP4 video files and uses a local LM Studio model (mimo-vl-7b-rl@q8_k_xl) to identify which videos contain kung fu practice or martial arts training.

## 🎯 What It Does

1. **Scans Folder**: Recursively looks for video files (.mp4, .avi, .mov, .mkv) in specified directory
2. **Extracts Thumbnails**: Uses FFmpeg to extract representative frames from each video
3. **Vision Analysis**: Sends actual video thumbnails (base64 encoded) to the local LM Studio vision model
4. **Content Detection**: AI analyzes visual content to identify kung fu/martial arts practice
5. **Results Collection**: Returns a clean array of filenames that match kung fu practice videos
6. **Error Handling**: Manages files that cannot be processed or analyzed

## 🛠️ Prerequisites

### LM Studio Setup
- **LM Studio** running locally on `localhost:1234`
- **Model**: `mimo-vl-7b-rl@q8_k_xl` (vision model) loaded and ready
- **Endpoint**: `http://localhost:1234/v1/chat/completions`

### FFmpeg Installation
- **FFmpeg** installed and available in system PATH
- Required for video thumbnail extraction
- Download: https://ffmpeg.org/download.html

### N8N Setup
- N8N instance running (local or Docker)
- Import the workflow JSON file
- Configure settings via config.json file

## 📁 Workflow Structure

### Nodes Overview
1. **Manual Trigger** - Starts the workflow manually
2. **Scan Folder for MP4s** - Finds all MP4 files in the target directory
3. **AI Video Analysis** - Sends video info to LM Studio for analysis
4. **Process AI Response** - Interprets the AI response
5. **Filter Kung Fu Videos** - Separates kung fu videos from others
6. **Collect Final Results** - Aggregates matching filenames
7. **Handle Errors** - Manages processing errors

### Data Flow
```
Manual Trigger → Scan Folder → AI Analysis → Process Response → Filter → Results
                                                                      ↓
                                                               Error Handler
```

## 🚀 Usage Instructions

### 1. Import Workflow
- Open N8N interface
- Go to Workflows → Import from File
- Select `kung_fu_video_workflow.json`

### 2. Configure Folder Path
Edit the "Scan Folder for MP4s" node:
```javascript
const folderPath = $input.first().json.folderPath || 'C:/Videos/KungFu';
```
Change the default path to your video folder.

### 3. Test LM Studio Connection
Ensure your LM Studio is:
- Running on `localhost:1234`
- Has `mimo-vl-7b-rl@q8_k_xl` model loaded
- Accepting API requests

### 4. Execute Workflow
- Click "Execute Workflow" in N8N
- The workflow will process all MP4 files in the folder
- Results appear in the "Collect Final Results" node

## 📊 Output Format

### Successful Results
```json
{
  "kungFuVideos": [
    "karate_practice_session.mp4",
    "kung_fu_forms_training.mp4",
    "martial_arts_sparring.mp4"
  ],
  "totalFound": 3,
  "timestamp": "2025-09-03T00:00:00.000Z",
  "summary": "Found 3 kung fu practice videos"
}
```

### Error Handling
```json
{
  "errors": [
    {
      "filename": "corrupted_video.mp4",
      "error": "Failed to analyze video"
    }
  ],
  "errorCount": 1,
  "timestamp": "2025-09-03T00:00:00.000Z"
}
```

## ⚙️ Customization Options

### Modify AI Prompt
Edit the "AI Video Analysis" node to change detection criteria:
```json
{
  "role": "user",
  "content": "Analyze this video file and determine if it shows kung fu practice or martial arts training. The video file is: {{ $json.filename }}. Please respond with only 'YES' if it shows kung fu/martial arts practice, or 'NO' if it does not."
}
```

### Add More File Types
Modify the file filter in "Scan Folder for MP4s":
```javascript
const videoFiles = files.filter(file => {
  const ext = path.extname(file).toLowerCase();
  return ['.mp4', '.avi', '.mov', '.mkv'].includes(ext);
});
```

### Adjust AI Response Processing
Update the "Process AI Response" node to handle different response formats:
```javascript
const isKungFu = aiResponse.toUpperCase().includes('YES') || 
                 aiResponse.toLowerCase().includes('kung fu') ||
                 aiResponse.toLowerCase().includes('martial arts');
```

## 🔧 Troubleshooting

### Common Issues

1. **LM Studio Not Responding**
   - Check if LM Studio is running
   - Verify model is loaded
   - Test endpoint: `curl http://localhost:1234/v1/models`

2. **Folder Access Errors**
   - Ensure N8N has read permissions for the video folder
   - Check folder path syntax (use forward slashes)

3. **No Videos Found**
   - Verify folder contains MP4 files
   - Check file extensions are lowercase

4. **AI Analysis Fails**
   - Increase timeout in HTTP Request node
   - Check LM Studio logs for errors
   - Verify model supports the request format

## 🎯 Performance Notes

- **Processing Speed**: Videos are analyzed one by one to avoid overwhelming the AI model
- **Timeout**: Set to 30 seconds per video analysis
- **Memory Usage**: Minimal - only filenames are processed, not video content
- **Scalability**: Can handle hundreds of videos (limited by LM Studio performance)

## 🔄 Future Enhancements

- Add support for video thumbnails/frames analysis
- Implement batch processing for faster execution
- Add confidence scoring for AI predictions
- Create detailed analysis reports
- Support for multiple martial arts styles detection
